from odoo import models, _
import datetime

class ResPartner(models.Model):
    _inherit = 'res.partner'

    def print_followup_report(self, report_lang_cn=False):
        lang = "en_US"
        report_name = "PartnerFollowup.xlsx"
        if report_lang_cn:
            lang = 'zh_CN'
            report_name = "PartnerFollowup_CN.xlsx"
        self = self.with_context(lang=lang)
        options = {
            'partner_id': self.id,
            'followup_line': self.followup_line_id,
            'context': self.env.context,
        }
        AccountFollowupReportObj = self.env["account.followup.report"]
        # 获取原始lines
        lines = AccountFollowupReportObj.with_context(print_mode=True)._get_followup_report_lines(options)
        # 增强：修正Date/Due Date/排序
        for line in lines:
            if line.get('type') == 'unreconciled_aml' and line.get('account_move'):
                aml = self.env['account.move.line'].browse(line['id'])
                move = aml.move_id
                # 非发票单据，Date和Due Date都用move.date
                if move.move_type not in ('out_invoice', 'in_invoice', 'out_refund', 'in_refund'):
                    for idx, col in enumerate(line['columns']):
                        if idx in (0, 1):  # Date和Due Date列
                            date = move.date and move.date.strftime('%d/%m/%Y') or ''
                            col['name'] = date
        # 按Date（第1列）排序
        def parse_date(line):
            try:
                return datetime.datetime.strptime(line['columns'][0]['name'], '%d/%m/%Y')
            except Exception:
                return datetime.datetime.max
        lines = sorted(lines, key=parse_date)
        # 生成报表
        report_data = {
            "customer": self.full_name,
            "code": self.code,
            "report_date": datetime.date.today().strftime("%d/%m/%Y"),
            "lines": lines,
        }
        # 复用原有xlsx生成逻辑
        return self.env['res.partner'].with_context(_from_followup_enhance=True)._print_followup_xlsx(report_data, report_name)

    def _print_followup_xlsx(self, report_data, report_name):
        # 复用原有custom_reports_artextile_jsi的xlsx生成逻辑
        import io, base64
        from odoo.tools.misc import xlsxwriter
        from odoo.tools import float_round, formatLang
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {"in_memory": True})
        bold = workbook.add_format({"bold": True})
        float_2dp = workbook.add_format({'num_format': '#,##0.00'})
        worksheet = workbook.add_worksheet()
        worksheet.write(0, 0, _(u"FOLLOWUP REPORT"), bold)
        worksheet.write(1, 0, _(u"Customer"), bold)
        worksheet.write(1, 1, report_data["customer"])
        worksheet.write(2, 0, _(u"Customer Code"), bold)
        worksheet.write(2, 1, report_data["code"])
        worksheet.write(3, 0, _(u"Report Date"), bold)
        worksheet.write(3, 1, report_data["report_date"])
        table_header = [_(u"Journal No."), _(u"Date"), _(u"Due Date"), _(u"Source Document"), _(u"Communication"), _(u"Amount"), _(u"Due amount")]
        row = 5
        col = 0
        for head in table_header:
            worksheet.write(row, col, head, bold)
            col += 1
        row = 6
        for line in report_data["lines"]:
            worksheet.write(row, 0, line.get("name"))
            col = 1
            for column in line["columns"]:
                if col in (1, 2):
                    worksheet.write(row, col, column['name'])
                elif col == 4:
                    worksheet.write(row, col, column['name'])
                elif col == 5:
                    total = (
                        line.get("account_move")
                        and formatLang(self.env, line["account_move"].amount_total, currency_obj=self.currency_id)
                        or ""
                    )
                    # 安全处理货币符号，确保是字符串
                    currency_symbol = self.currency_id.symbol or ''
                    if isinstance(currency_symbol, bool):
                        currency_symbol = ''

                    total = str(total).replace(currency_symbol, '').replace(',', '').strip()
                    column_name = str(column['name']).replace(',', '').replace(currency_symbol, '').strip()

                    if total and total != '':
                        try:
                            worksheet.write_number(row, col, float_round(float(total), precision_digits=2), float_2dp)
                        except (ValueError, TypeError):
                            worksheet.write(row, col, total)

                    if column_name and column_name != '':
                        try:
                            worksheet.write_number(row, col + 1, float_round(float(column_name), precision_digits=2), float_2dp)
                        except (ValueError, TypeError):
                            worksheet.write(row, col + 1, column_name)
                else:
                    worksheet.write(row, col, column["name"])
                col += 1
            row += 1
        workbook.close()
        xlsx_data = output.getvalue()
        file_data = base64.encodebytes(xlsx_data)
        followup_report = self.env['res.partner.followup.report'].sudo().create({"file_data": file_data})
        return {
            "type": "ir.actions.act_url",
            "url": "/web/content/?model={}&id={}&field=file_data&filename={}&download=true".format(
                followup_report._name, followup_report.id, report_name
            ),
            "target": "self",
        } 