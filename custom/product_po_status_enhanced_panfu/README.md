# Product PO Status Enhanced - panfu

**Author**: panfu  
**Dependencies**: product_misc_jsi  
**Asana Task ID**: 1210669684218343

## Feature Overview
Extends the PO QTY button (action_view_pending_po_moves) on the product detail page to support displaying purchase order lines with ["waiting", "confirmed", "partially_available", "assigned"] status, meeting business requirements.

## 安装与使用
1. 将本插件放置于 `custom_addons` 目录下。
2. 在 Odoo 应用列表中更新并安装本插件。
3. 商品详情页的 PO QTY 按钮将自动生效，无需额外配置。

## 变更说明
- 仅扩展 PO QTY 按钮的筛选逻辑，不影响原有数据和界面。 