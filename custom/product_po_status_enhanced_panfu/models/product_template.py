from odoo import models, api
from odoo.osv.expression import AND
# import logging

# _logger = logging.getLogger(__name__)

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    def action_view_pending_po_moves(self):
        self.ensure_one()
        location_ptn_vendor = self.env.ref("stock.stock_location_suppliers")
        
        # 使用stock.move而不是move.line，避免所有视图问题
        moves = self.env['stock.move'].search([
            ('picking_type_id.code', '=', 'incoming'),
            ('state', 'in', ['waiting', 'confirmed', 'partially_available', 'assigned']),
            ('location_id', '=', location_ptn_vendor.id),
            ('product_id.product_tmpl_id', '=', self.id),
        ])
        
        # 使用完全自定义的视图和过滤
        action = {
            'name': 'Pending PO Moves',
            'type': 'ir.actions.act_window',
            'res_model': 'stock.move',
            'view_mode': 'tree,form',
            'domain': [
                ('picking_type_id.code', '=', 'incoming'),
                ('state', 'in', ['waiting', 'confirmed', 'partially_available', 'assigned']),
                ('location_id', '=', location_ptn_vendor.id),
                ('product_id.product_tmpl_id', '=', self.id),
            ],
            'context': {
                'create': 0,
                'search_default_product_id': self.product_variant_ids[0].id,
            },
            'views': [
                (self.env.ref('product_po_status_enhanced_panfu.view_stock_move_tree_po_enhanced').id, 'tree'),
                (False, 'form')
            ],
            'target': 'current',
        }
        return action

    def get_moves(self, product_ids, extra_domain):
        """Override get_moves to include 'waiting' state for PO Qty calculation"""
        # _logger.warning(f"=== ENHANCED: get_moves called with product_ids: {product_ids}")
        # _logger.warning(f"=== ENHANCED: extra_domain: {extra_domain}")
        
        # 忽略extra_domain中的过滤条件，只使用我们的过滤
        domain = [
            ("picking_code", "=", "incoming"),
            ("location_id.usage", "=", "supplier"),
            ("product_id.product_tmpl_id", "in", product_ids),
            ("state", "in", ["waiting", "confirmed", "partially_available", "assigned"]),
        ]
        
        result = self.env["stock.move"].read_group(domain, ["product_uom_qty", "reserved_availability"], ["product_id"])
        # _logger.warning(f"=== ENHANCED: get_moves result: {result}")
        return result 