from odoo import models, api
from odoo.osv.expression import AND
# import logging

# _logger = logging.getLogger(__name__)

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    def action_view_pending_po_moves(self):
        self.ensure_one()
        location_ptn_vendor = self.env.ref("stock.stock_location_suppliers")

        # 使用标准的 stock.move.line action
        action = self.env["ir.actions.actions"]._for_xml_id("stock.stock_move_line_action")

        # 修改为 stock.move.line 的 domain
        action['domain'] = [
            ('move_id.picking_type_id.code', '=', 'incoming'),
            ('move_id.state', 'in', ['waiting', 'confirmed', 'partially_available', 'assigned']),
            ('move_id.location_id', '=', location_ptn_vendor.id),
            ('product_id.product_tmpl_id', '=', self.id),
        ]

        # 设置 context
        action['context'] = {
            'create': 0,
            'search_default_product_id': self.product_variant_ids[0].id if self.product_variant_ids else False,
        }

        # 设置名称
        action['name'] = 'Pending PO Move Lines'
        action['target'] = 'current'

        return action

    def get_moves(self, product_ids, extra_domain):
        """Override get_moves to include 'waiting' state for PO Qty calculation"""
        # _logger.warning(f"=== ENHANCED: get_moves called with product_ids: {product_ids}")
        # _logger.warning(f"=== ENHANCED: extra_domain: {extra_domain}")
        
        # 忽略extra_domain中的过滤条件，只使用我们的过滤
        domain = [
            ("picking_code", "=", "incoming"),
            ("location_id.usage", "=", "supplier"),
            ("product_id.product_tmpl_id", "in", product_ids),
            ("state", "in", ["waiting", "confirmed", "partially_available", "assigned"]),
        ]
        
        result = self.env["stock.move"].read_group(domain, ["product_uom_qty", "reserved_availability"], ["product_id"])
        # _logger.warning(f"=== ENHANCED: get_moves result: {result}")
        return result 