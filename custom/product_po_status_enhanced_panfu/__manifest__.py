{
    'name': 'Product PO Status Enhanced - panfu',
    'version': '********.0',
    'author': 'panfu',
    'category': 'Inventory/Inventory',
    'summary': 'Extend PO QTY button to support multi-status display',
    'description': '''
        Extend product detail page PO QTY button (action_view_pending_po_moves),
        to support displaying purchase order lines with ["waiting", "confirmed", "partially_available", "assigned"] status,
        meeting Asana task 1210669684218343 requirements.
    ''',
    'depends': [
        'product_misc_jsi',
        'stock',
    ],
    'sequence': 1000,  # Ensure this loads after product_misc_jsi
    'data': [
        'views/stock_move_views.xml',
    ],
} 