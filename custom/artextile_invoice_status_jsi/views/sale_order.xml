<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_order_form_partner_misc_jsi" model="ir.ui.view">
            <field name="name">sale.order.form.partner.misc.jsi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form" />
            <field name="arch" type="xml">
                <field name="journal_id" position="after">
                    <field name="invoice_status_ar" />
                </field>
            </field>
        </record>
        <record id="view_order_tree_partner_misc_jsi" model="ir.ui.view">
            <field name="name">sale.order.tree.partner.misc.jsi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.sale_order_tree" />
            <field name="arch" type="xml">
                <field name="invoice_status" position="after">
                    <field
                        name="invoice_status_ar"
                        decoration-success="invoice_status_ar in ['invoiced', 'credit_note_posted']"
                        decoration-info="invoice_status_ar in ['to invoice', 'to_credit_note']"
                        decoration-warning="invoice_status_ar == 'credit_note_created'"
                        widget="badge"
                        optional="show"
                    />
                </field>
                <field name="invoice_status" position="attributes">
                    <attribute name="invisible">1</attribute>
                </field>
            </field>
        </record>
    </data>
</odoo>
