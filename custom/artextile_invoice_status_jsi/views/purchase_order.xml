<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="purchase_order_view_tree_artextile_invoice_status_jsi" model="ir.ui.view">
            <field name="name">purchase.order.view.tree.artextile.invoice.status.jsi</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_view_tree" />
            <field name="arch" type="xml">
                <field name="invoice_status" position="after">
                    <field
                        name="invoice_status_ar"
                        widget="badge"
                        decoration-success="invoice_status_ar in ['invoiced', 'credit_note_posted']"
                        decoration-info="invoice_status_ar in ['to invoice', 'to_credit_note']"
                        decoration-warning="invoice_status_ar == 'credit_note_created'"
                        optional="show"
                    />
                </field>
                <field name="invoice_status" position="attributes">
                    <attribute name="invisible">1</attribute>
                </field>
            </field>
        </record>
        <record id="purchase_order_form_artextile_invoice_status_jsi" model="ir.ui.view">
            <field name="name">purchase.order.form.artextile.invoice.status.jsi</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form" />
            <field name="arch" type="xml">
                <field name="invoice_status" position="after">
                    <field name="invoice_status_ar" />
                </field>
                <field name="invoice_status" position="attributes">
                    <attribute name="invisible">1</attribute>
                </field>
            </field>
        </record>
    </data>
</odoo>
