from odoo import api, fields, models


class SaleOrder(models.Model):
    _inherit = "sale.order"

    invoice_status_ar = fields.Selection(
        [
            ("invoiced", "Fully Invoiced"),
            ("to invoice", "To Invoice"),
            ("no", "Nothing to Invoice"),
            ("to_credit_note", "To Credit Note"),
            ("credit_note_created", "Credit Note Created"),
            ("credit_note_posted", "Credit Note Posted"),
        ],
        string="Artextile Invoice Status",
        compute="_compute_invoice_status_ar",
    )

    @api.depends()
    def _compute_invoice_status_ar(self):
        for order in self:
            if order.state != "sale":
                order.invoice_status_ar = "no"
                continue
            invoice_status_ar = False
            return_picking = order.picking_ids.filtered(
                lambda x: x.picking_type_code == "incoming" and x.state != "cancel"
            )
            if return_picking:
                credit_invoice_draft = order.invoice_ids.filtered(
                    lambda x: x.move_type == "out_refund" and x.state == "draft"
                )
                credit_invoice_done = order.invoice_ids.filtered(
                    lambda x: x.move_type == "out_refund" and x.state == "posted"
                )
                if credit_invoice_draft:
                    invoice_status_ar = "credit_note_created"
                elif credit_invoice_done:
                    invoice_status_ar = "credit_note_posted"
                elif any(
                    line.qty_delivered < line.product_uom_qty and line.qty_invoiced == line.product_uom_qty
                    for line in order.order_line.filtered(lambda l: not l.display_type)
                ):
                    invoice_status_ar = "to_credit_note"
            if invoice_status_ar:
                order.invoice_status_ar = invoice_status_ar
            elif all(
                line.qty_delivered == 0 and line.qty_invoiced == 0
                for line in order.order_line.filtered(lambda l: not l.display_type)
            ):
                order.invoice_status_ar = "no"
            elif any(
                line.qty_invoiced == 0 or line.qty_invoiced < line.qty_delivered
                for line in order.order_line.filtered(lambda l: not l.display_type)
            ):
                order.invoice_status_ar = "to invoice"
            elif all(
                line.product_uom_qty == line.qty_delivered and line.product_uom_qty == line.qty_invoiced
                for line in order.order_line.filtered(lambda l: not l.display_type)
            ):
                order.invoice_status_ar = "invoiced"
            else:
                order.invoice_status_ar = "no"
