from odoo import api, fields, models


class PaymentMethod(models.Model):
    _inherit = 'payment.method'

    default_add_to_partner = fields.<PERSON><PERSON><PERSON>("Default Add to Partner")

    @api.model
    def _get_compatible_payment_methods(self, *args, sale_order_id=None, **kwargs):
        """ OVERRIDE
            We need to render Only Payment Method that are allowed for the respected partner
            Main methos will filter payment method by country, currency...
            This method will filter the payment method by partner
            All payment page must satisfy this custom rule
            Used in
                - website checkout page
                - Payment link (generated from Invoice)
                - Portal page
        """
        res = super()._get_compatible_payment_methods(*args, sale_order_id=sale_order_id, **kwargs)
        if sale_order_id:
            partner = self.env['sale.order'].browse(sale_order_id).partner_id
        elif kwargs.get('invoice_id'):
            partner = self.env['account.move'].browse(kwargs.get('invoice_id')).partner_id
        elif args[1] is not None:
            partner = self.env['res.partner'].browse(args[1])
        # res will have all Payment method after filter(contry, currency...)
        # We only return the payment methods that are in the partner's list as well as in res
        if partner and partner.payment_method_ids:
            res = res & partner.payment_method_ids
        return res
