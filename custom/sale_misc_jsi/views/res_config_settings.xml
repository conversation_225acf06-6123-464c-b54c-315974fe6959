<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="res_config_settings_view_form_sale_misc_jsi" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.sale.misc.jsi</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="sale.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <block name="catalog_setting_container" position="before">
                <block title="Artextile Setting Panel" name="ar_custom_setting_container">
                    <setting help="Warn customer if product's Free Inv. less than SO Quantity Limit">
                        <field name="use_so_qty_limit"/>
                        <div class="content-group" invisible="not use_so_qty_limit">
                            <div class="mt16">
                                <label for="so_qty_limit" class="o_light_label mr8"/>
                                <field name="so_qty_limit" class="oe_inline"/>
                                <span class="fa fa-lg fa-building-o p-2" title="Values set here are company-specific." groups="base.group_multi_company"/>
                            </div>
                        </div>
                    </setting>
                    <setting help="Warn customer if duplicated order line found within given days range">
                        <field name="use_so_days_limit"/>
                        <div class="content-group" invisible="not use_so_days_limit">
                            <div class="mt16">
                                <label for="so_days_limit" class="o_light_label mr8"/>
                                <field name="so_days_limit" class="oe_inline"/>
                                <span class="fa fa-lg fa-building-o p-2" title="Values set here are company-specific." groups="base.group_multi_company"/>
                            </div>
                        </div>
                    </setting>
                </block>
            </block>
        </field>
    </record>
</odoo>
