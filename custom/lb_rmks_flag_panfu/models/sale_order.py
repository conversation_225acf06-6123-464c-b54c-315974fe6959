from odoo import models, api

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _get_lb_rmks_prefix(self):
        """获取 LB Rmks 前缀"""
        if self.partner_shipping_id:
            flag = self.partner_shipping_id.lb_rmks_flag
            if flag == 'zheng':
                return '正；'
            elif flag == 'fan':
                return '反；'
        return ''

    def _apply_lb_rmks_prefix_to_lines(self):
        """为所有订单行应用 LB Rmks 前缀"""
        prefix = self._get_lb_rmks_prefix()
        if prefix:
            for line in self.order_line:
                current_value = line.remarks or ''
                # 检查是否已经有前缀，避免重复添加
                if not current_value.startswith(('正；', '反；')):
                    line.remarks = f'{prefix}{current_value}'

    @api.onchange('partner_shipping_id')
    def _onchange_partner_shipping_id_lb_rmks_flag(self):
        """送货地址改变时，为所有订单行应用前缀"""
        self._apply_lb_rmks_prefix_to_lines()

    @api.onchange('order_line')
    def _onchange_order_line_lb_rmks_flag(self):
        """订单行改变时，为新增的订单行应用前缀"""
        self._apply_lb_rmks_prefix_to_lines()


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    @api.model_create_multi
    def create(self, vals_list):
        """创建订单行时自动应用 LB Rmks 前缀"""
        lines = super().create(vals_list)
        for line in lines:
            if line.order_id and line.order_id.partner_shipping_id:
                prefix = line.order_id._get_lb_rmks_prefix()
                if prefix:
                    current_value = line.remarks or ''
                    if not current_value.startswith(('正；', '反；')):
                        line.remarks = f'{prefix}{current_value}'
        return lines