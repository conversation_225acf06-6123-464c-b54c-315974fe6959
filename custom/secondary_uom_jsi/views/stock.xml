<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_picking_form_secondary_uom_jsi" model="ir.ui.view">
            <field name="name">stock.picking.form.multi.uom.jsi</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock_delivery.view_picking_withcarrier_out_form" />
            <field name="priority">99</field>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='move_ids_without_package']/tree/field[@name='product_id']" position="after">
                    <field name="secondary_uom_qty" optional="show" />
                    <field name="secondary_uom_id" optional="show" />
                </xpath>
                <field name="location_id" position="after">
                    <label for="carrier_tracking_ref" position="move"/>
                    <div name="tracking" position="move"/>
                </field>
            </field>
        </record>
        <record id="view_stock_move_line_detailed_operation_tree_secondary_uom_jsi" model="ir.ui.view">
            <field name="name">view.stock.move.line.detailed.operation.tree.multi.uom.jsi</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree" />
            <field name="arch" type="xml">
                <field name="product_id" position="after">
                    <field name="secondary_uom_qty" optional="show" />
                    <field name="secondary_uom_id" optional="show" />
                </field>
            </field>
        </record>
        <record id="choose_delivery_carrier_view_form_secondary_uom_jsi" model="ir.ui.view">
            <field name="name">choose.delivery.carrier.view.form.secondary.uom.jsi</field>
            <field name="model">choose.delivery.carrier</field>
            <field name="inherit_id" ref="delivery.choose_delivery_carrier_view_form" />
            <field name="arch" type="xml">
                <!-- Make it invisible as it was not in v14 + it show different total which make client confuse -->
                <xpath expr="//div[@name='carried_weight']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//label[@name='carried_weight_label']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
