<?xml version='1.0' encoding='utf-8' ?>
<odoo>
<data>
    <record id="purchase_order_form_lot_cust_jsi" model="ir.ui.view">
        <field name="name">purchase.order.form.lot.cust.jsi</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form" />
        <field name="arch" type="xml">
            <xpath expr="//tree/field[@name='product_id']" position="after">
                <field name="selvedge_id"/>
            </xpath>
            <xpath expr="//tree/field[@name='product_qty']" position="attributes">
                    <attribute name="digits">[12, 3]</attribute>
                </xpath>
        </field>
    </record>
</data>
</odoo>
