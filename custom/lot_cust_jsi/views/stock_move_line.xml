<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_stock_move_line_operation_tree_lot_cust_jsi" model="ir.ui.view">
            <field name="name">stock.move.line.operations.tree.lot.cust.jsi</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree" />
            <field name="arch" type="xml">
                <field name="quantity" position="after">
                    <field name="gross_unit" column_invisible="not parent.show_lots_text" />
                    <field name="selvedge_id" column_invisible="not parent.show_lots_text" />
                </field>
                <field name="lot_id" position="attributes">
                    <attribute name="domain">
                        [('quant_ids.location_id', 'in', [location_id]), ('product_id', '=', product_id), ('company_id',
                        '=', company_id)]
                    </attribute>
                </field>
                <field name="lot_id" position="after">
                    <xpath expr="//field[@name='location_dest_id'][2]" position="move" />
                    <field name="lot_qty_available" optional="show" sum="Available Quantity"/>
                </field>
            </field>
        </record>
        <record id="view_stock_move_line_detailed_operation_tree_lot_cust_jsi" model="ir.ui.view">
            <field name="name">stock.move.line.operations.tree.lot.cust.jsi</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree" />
            <field name="arch" type="xml">
                <!-- Make tree red if display_warning is True -->
                <xpath expr="//tree" position="attributes">
                    <attribute name="decoration-danger">display_warning</attribute>
                </xpath>
                <field name="lot_id" position="before">
                    <field name="gross_unit" column_invisible="not context.get('show_lots_text')" />
                    <field name="selvedge_id" column_invisible="not context.get('show_lots_text')" />
                </field>
                <field name="lot_id" position="attributes">
                    <attribute name="domain">
                        [('quant_ids.location_id', 'in', [location_id]), ('product_id', '=', product_id), ('company_id',
                        '=', company_id)]
                    </attribute>
                </field>
                <field name="lot_id" position="after">
                    <field name="lot_qty_available" optional="show" sum="Available Quantity"/>
                    <field name="display_warning" string="Qty diff?" optional="show" />
                </field>
                <field name="quantity" position="attributes">
                    <attribute name="sum">"Quantity"</attribute>
                </field>
            </field>
        </record>
        <record id="view_move_selvedge_tree_lot_cust_jsi" model="ir.ui.view">
            <field name="name">view.move.selvedge.tree.lot.cust.jsi</field>
            <field name="model">move.selvedge</field>
            <field name="arch" type="xml">
                <tree string="Selvedge" editable="top">
                    <field name="name" />
                    <field name="description" />
                </tree>
            </field>
        </record>
        <record id="action_move_selvedge_lot_cust_jsi" model="ir.actions.act_window">
            <field name="name">Selvedge</field>
            <field name="res_model">move.selvedge</field>
            <field name="view_mode">tree</field>
        </record>
        <menuitem
            name="Selvedge"
            action="action_move_selvedge_lot_cust_jsi"
            id="menu_action_move_selvedge_lot_cust_jsi"
            parent="stock.menu_stock_inventory_control"
        />
    </data>
</odoo>
