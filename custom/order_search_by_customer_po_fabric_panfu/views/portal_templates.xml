<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <!-- Extend the quotations template to add customer PO and fabric number columns -->
    <template id="portal_my_quotations_extended" inherit_id="sale.portal_my_quotations" name="Extended Portal Quotations">
        <xpath expr="//thead/tr" position="inside">
            <th>Customer PO</th>
            <th>Fabric #</th>
        </xpath>
        <xpath expr="//t[@t-foreach='quotations']/tr" position="inside">
            <td><span t-field="quotation.customer_po"/></td>
            <td><span t-field="quotation.fabric_number"/></td>
        </xpath>
    </template>

    <!-- Extend the orders template to add customer PO and fabric number columns -->
    <template id="portal_my_orders_extended" inherit_id="sale.portal_my_orders" name="Extended Portal Orders">
        <xpath expr="//thead/tr" position="inside">
            <th>Customer PO</th>
            <th>Fabric #</th>
        </xpath>
        <xpath expr="//t[@t-foreach='orders']/tr" position="inside">
            <td><t t-esc="customer_po_agg_dict.get(order.id, '')"/></td>
            <td><span t-field="order.fabric_number"/></td>
        </xpath>
    </template>

    <!-- 扩展原始搜索框支持客户采购单号和面料编号搜索 -->
    <template id="portal_searchbar_extend_customer_po_fabric" inherit_id="portal.portal_searchbar" name="Extend Portal Searchbar with Customer PO and Fabric">
        <!-- 修改搜索框的占位符提示 -->
        <xpath expr="//input[@name='search']" position="attributes">
            <attribute name="placeholder">搜索订单号、客户采购单号或面料编号</attribute>
        </xpath>
    </template>

    <!-- 保留原有扩展订单列表和表头的模板 -->
</odoo>