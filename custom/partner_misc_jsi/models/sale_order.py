from odoo import api, fields, models
from odoo.fields import Command


class SaleOrder(models.Model):
    _inherit = "sale.order"

    @api.depends("partner_id")
    def _compute_related_child_ids(self):
        for sale in self:
            partner_ids = sale.partner_id.child_ids.ids + self.partner_id.ids
            sale.related_child_ids = [Command.set(partner_ids)]

    related_child_ids = fields.Many2many("res.partner", "Child Partners", compute="_compute_related_child_ids")

    def _compute_partner_shipping_id(self):
        """ OVERRODE
            Force to set partner_id into partner_shipping_id
        """
        for order in self:
            order.partner_shipping_id = order.partner_id
