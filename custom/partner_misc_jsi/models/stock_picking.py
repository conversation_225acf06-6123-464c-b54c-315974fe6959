from odoo import api, fields, models
from odoo.fields import Command


class StockPicking(models.Model):
    _inherit = "stock.picking"

    """ Why this field: Here we need to apply `readonly` attributes to the move_ids_without_package only if it belongs to group: group_block_add_a_line
        As we can not add groups_id anymore in our inherited view definition: https://github.com/odoo/odoo/blob/17.0/odoo/addons/base/models/ir_ui_view.py#L435
        We can do it by defining same field 2 times, with and without group.
        But move_ids_without_package is o2m field with long tree view inside. So I need to copy whole tree in my second field
        By adding new tree and use its ref in both field is still same"""

    is_block_adding_line = fields.Bo<PERSON>an(compute="_compute_is_block_adding_line")

    def _compute_is_block_adding_line(self):
        for picking in self:
            picking.is_block_adding_line = self.env.user.has_group('partner_misc_jsi.group_block_add_a_line')
