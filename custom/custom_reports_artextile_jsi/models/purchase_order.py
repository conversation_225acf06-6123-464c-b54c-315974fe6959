from odoo import models


class PurchaseOrderLine(models.Model):
    _inherit = "purchase.order"

    def get_qty_group_by_uom(self):
        """
        Prepare the dict of total quantity per UOM for report
        """
        uom_groups = {}
        line_datas = self.env["purchase.order.line"].read_group(
            [("order_id", "=", self.id)], ["product_qty"], ["product_uom"]
        )
        for data in line_datas:
            qty = (
                int(data["product_qty"])
                if data["product_qty"] == int(data["product_qty"])
                else "%.3f" % data["product_qty"]
            )
            uom_groups.update({data["product_uom"][1]: qty})
        return uom_groups
