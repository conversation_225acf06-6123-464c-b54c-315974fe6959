<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <template id="report_purchaseorder_document_artextile_jsi" inherit_id="purchase.report_purchaseorder_document">
        <t t-call="web.external_layout" position="replace">
            <t t-call="custom_reports_artextile_jsi.external_layout_artextile_jsi">
                <div class="page PMingLiU_font font-size14">
                    <style>th, td {padding: 4px !important;}</style>
                    <table class="product-table-ar table-borderless">
                        <thead style="display: table-row-group">
                            <tr class="text-center text-nowrap">
                                <th><span>Line<br />行號</span></th>
                                <th><span>Item #<br />貨號</span></th>
                                <th><span>Factory #<br />廠號碼</span></th>
                                <th><span>Description<br />摘要</span></th>
                                <th><span>Quantity<br />數量</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-set="line_no" t-value="1" />
                            <t t-foreach="o.order_line" t-as="line">
                                <tr
                                    style="text-align: center;"
                                    t-att-class="'bg-200 font-weight-bold o_line_section' if line.display_type == 'line_section' else 'font-italic o_line_note' if line.display_type == 'line_note' else ''"
                                >
                                    <t t-if="not line.display_type">
                                        <td>
                                            <span t-out="line_no" />
                                        </td>
                                        <td>
                                            <span t-field="line.product_id.default_code" />
                                        </td>
                                        <td>
                                            <span t-field="line.product_id.vendor_item_no" />
                                        </td>
                                        <td>
                                            <span t-field="line.name" />
                                        </td>
                                        <td class="text-end">
                                            <t t-if="int(line.product_qty) == line.product_qty">
                                                <span t-out="int(line.product_qty)" />
                                            </t>
                                            <t t-else="">
                                                <span t-field="line.product_qty" t-options="{'widget': 'float', 'precision': 3}" />
                                            </t>
                                            <span t-field="line.product_uom" />
                                        </td>
                                        <t t-set="line_no" t-value="line_no + 1" />
                                    </t>
                                    <t t-if="line.display_type == 'line_section'">
                                        <td colspan="99" id="section" class="text-start">
                                            <span t-field="line.name" />
                                        </td>
                                    </t>
                                    <t t-if="line.display_type == 'line_note'">
                                        <td colspan="99" id="note" class="text-start">
                                            <span t-field="line.name" />
                                        </td>
                                    </t>
                                </tr>
                            </t>
                            <t t-set="uom_groups" t-value="o.get_qty_group_by_uom()" />
                            <tr t-foreach="uom_groups" t-as="uom_group">
                                <td colspan="3" style="border: 0px;" />
                                <td
                                    t-if="uom_group_index == 0"
                                    t-att-rowspan="len(uom_groups)"
                                    class="text-center align-middle"
                                >
                                    <strong>Total Quantity 總數量</strong>
                                </td>
                                <td class="text-end fw-bold">
                                    <span t-out="uom_groups[uom_group]" />
                                    <span t-out="uom_group" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </t>
        </t>
    </template>
</odoo>
