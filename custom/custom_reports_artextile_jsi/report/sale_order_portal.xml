<?xml version='1.0' encoding='utf-8' ?>
<odoo>

    <template id="sale_order_portal_content_artextile_jsi" inherit_id="sale.sale_order_portal_content" priority="99">
        <!-- Old address style -->
        <div id="customer_info" position="replace">
            <div id="customer_info" class="row mb-4">
                <div class="col-lg-6">
                    <strong>Invoicing Address:</strong>
                    <div t-field="sale_order.partner_invoice_id"/>
                    <span>Company Name: </span> <span t-field="sale_order.partner_invoice_id.full_name"/> <br/>
                    <t t-call="website_artextile_jsi.common_address_format_website_artextile_jsi"><t t-set="address_partner" t-value="sale_order.partner_invoice_id"/></t>
                </div>
                <div class="col-lg-6">
                    <strong>Shipping Address:</strong>
                    <div>Address Label: <span t-field="sale_order.partner_shipping_id.name"/></div>
                    <t t-call="website_artextile_jsi.common_address_format_website_artextile_jsi"><t t-set="address_partner" t-value="sale_order.partner_shipping_id"/></t>
                </div>
            </div>
        </div>

        <!-- remove Taxes -->
        <th id="taxes_header" position="replace" />
        <td id="taxes" position="replace" />

        <!-- Add customer_po -->
        <th id="product_name_header" position="after">
            <th class="text-start">Customer PO</th>
        </th>
        <td id="product_name" position="after">
            <td><span t-field="line.customer_po"/></td>
        </td>

        <!-- Add default_code -->
        <th id="product_name_header" position="before">
            <th class="text-start">Internal Ref.</th>
        </th>
        <td id="product_name" position="before">
            <td><span t-field="line.product_id.default_code"/></td>
        </td>

        <!-- 3 decimal places-->
        <span t-field="line.product_uom_qty" position="attributes">
            <attribute name="t-options">{'widget': 'float', 'precision': 3}</attribute>
        </span>

        <!-- Amount (HKD) -->
        <xpath expr="//th[@id='subtotal_header']/span" position="replace">
            <span>Amount (HKD)</span>
        </xpath>

        <!-- Remove Delivery info -->
        <div t-if="delivery_orders" position="replace"/>

        <section id="signature" position="before">
            <section class="mt-5" t-if="sale_order.courier_tracking">
                <h3 class="">Courier Tracking</h3>
                <hr class="mt-0 mb-1"/>
                <span t-field="sale_order.courier_tracking"/>
            </section>
        </section>
    </template>
</odoo>
