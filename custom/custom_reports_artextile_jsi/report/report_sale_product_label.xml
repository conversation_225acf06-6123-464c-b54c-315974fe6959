<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <template id="report_sale_product_label_custom_reports_artextile_jsi">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-foreach="doc.order_line.filtered(lambda x: not x.is_label_printed)" t-as="line">
                        <t t-call="web.basic_layout">
                            <style>
                                @font-face{
                                    font-family: 'PMingLiU';
                                    src: url('/static/src/fonts/PMingLiU.ttf') format('truetype');
                                }
                                .PMingLiU_font {
                                    font-family: 'PMingLiU' !important;
                                }
                                .detail-div {
                                    text-align: center;
                                    border-top: 1px solid gray;
                                    width: 110px;
                                }
                                .mp-0 {margin: 0px !important; padding:0px !important;}
                            </style>
                            <div class="page text-center PMingLiU_font" style="margin-left: -7px !important;margin-right: -7px !important;">
                                <div style="text-align: -webkit-center;white-space: nowrap;overflow: hidden;width: 110px;">
                                    <div style="height: 20px;padding-top: 15px;vertical-align: middle;display: table-cell;font-size: 12px;">
                                        <span t-field="line.customer_po" />
                                    </div>
                                </div>
                                <div class="detail-div" style="height: 35px;white-space: nowrap;overflow: hidden;width: 110px;">
                                    <t t-if="line.product_id.default_code and len(line.product_id.default_code) &lt; 7">
                                        <span style="font-size: 29px;" t-field="line.product_id.default_code" />
                                    </t>
                                    <t t-else="">
                                        <span style="font-size: 22px;" t-field="line.product_id.default_code" />
                                    </t>
                                </div>
                                <div class="text-center" style="font-size: 23px;height: 30px;white-space: nowrap;">
                                    <span t-out="(line.product_uom_qty % 1 == 0) and int(line.product_uom_qty) or line.product_uom_qty"/>
                                    <span t-field="line.product_uom" />
                                </div>
                                <div class="text-end" style="height: 20px;margin-bottom: 4px;white-space: nowrap;overflow: hidden;">
                                    <span t-field="line.order_partner_id.short_name" />
                                </div>
                                <div class="detail-div text-start" style="font-size: 12px;white-space: nowrap;overflow: hidden;padding-top: 7px;">
                                    <span t-field="line.remarks" style="display: table-cell;" />
                                </div>
                                <div t-if="line.product_id.rank_number" class="text-start" style="font-size: 14px;white-space: nowrap;overflow: hidden;padding-top: 0px;">
                                    (<span t-field="doc.date_order" t-options='{"format": "Hm"}'/><span t-field="line.product_id.rank_number"/>)
                                </div>
                            </div>
                        </t>
                        <t t-set="_write" t-value="line.write({'no_of_label_printed': line.no_of_label_printed+1, 'is_label_printed': True})"/>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>
