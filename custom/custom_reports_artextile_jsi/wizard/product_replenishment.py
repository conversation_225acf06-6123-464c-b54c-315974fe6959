import base64
import io
import logging
from datetime import date, datetime

from odoo import api, fields, models
from odoo.osv.expression import AND
from odoo.tools.misc import xlsxwriter


class ProductReplenishmentReport(models.TransientModel):
    _name = "product.replenishment.report"
    _description = "Product Replenishment Report"

    @api.model
    def _set_default_product_ids(self):
        return self.env.context.get("active_ids", False)

    product_ids = fields.Many2many("product.template", string="Products", default=_set_default_product_ids)
    bulk_threshold = fields.Integer("Bulk Threshold (mt)", default=15)
    start_date = fields.Date("From Date")
    end_date = fields.Date("To Date")
    report_type = fields.Selection([("xlsx", "Excel"), ("pdf", "PDF")], string="Print-out Type", default="xlsx")
    file_data = fields.Binary(readonly=True, attachment=False)

    def generate_report(self):
        # Header
        start_date = date.strftime(self.start_date, "%d/%B/%Y")
        end_date = date.strftime(self.end_date, "%d/%B/%Y")
        reporting_date = date.today().strftime("%d/%B/%Y")
        header_data = {
            "reporting_date": reporting_date,
            "start_date": start_date,
            "end_date": end_date,
            "bulk_threshold": self.bulk_threshold,
        }

        report_data = self._prepare_report_data()
        if self.report_type == "pdf":
            return (
                self.env.ref("custom_reports_artextile_jsi.action_report_replenishment_artextile_jsi")
                .with_context(
                    header_data=header_data,
                    report_data=report_data,
                )
                .report_action([], data={"data": {}}, config=False)
            )
        else:
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {"in_memory": True})
            bold = workbook.add_format({"bold": True})
            worksheet = workbook.add_worksheet()
            worksheet.write(2, 1, ("Reporting Date: %s" % reporting_date))
            worksheet.write(3, 1, ("Bulk threshold: %s" % self.bulk_threshold))
            worksheet.write(4, 1, ("Sales Period 期間:From 自: %s To ⾄ %s" % (start_date, end_date)))
            row = 6
            table_header = [
                "Internal reference",
                "Master No.",
                "Total CL",
                "Total Bulk",
                "Mth CL",
                "Mth Bulk",
                "Sust. Mth(OH)",
                "Sust. Mth(OH+PO)",
                "Free inventory",
                "PO",
                "Quick service?",
                "Vendor",
                "Vendor product code",
                "Attribute details",
                "Product name",
                "Version family",
            ]
            col = 0
            for head in table_header:
                worksheet.write(row, col, head, bold)
                col += 1
            row += 1
            for line in report_data:
                worksheet.write(row, 0, line["default_code"])
                worksheet.write(row, 1, line["master_no"])
                worksheet.write(row, 2, float("%.2f" % line["total_cl"]))
                worksheet.write(row, 3, float("%.2f" % line["total_bk"]))
                worksheet.write(row, 4, float("%.2f" % line["mth_sales_cl"]))
                worksheet.write(row, 5, float("%.2f" % line["mth_sales_bk"]))
                worksheet.write(row, 6, float("%.2f" % line["sust_mth_oh"]))
                worksheet.write(row, 7, float("%.2f" % line["sust_mth_oh_po"]))
                worksheet.write(row, 8, float("%.2f" % line["free_inventory"]))
                worksheet.write(row, 9, float("%.2f" % line["po"]))
                worksheet.write(row, 10, line["quick_service"])
                worksheet.write(row, 11, line["vendor"])
                worksheet.write(row, 12, line["vendor_item_no"])
                worksheet.write(row, 13, line["attribute_details"])
                worksheet.write(row, 14, line["product_name"])
                worksheet.write(row, 15, line["version_family"])
                row += 1
            workbook.close()
            xlsx_data = output.getvalue()
            self.file_data = base64.encodebytes(xlsx_data)
            return {
                "type": "ir.actions.act_url",
                "url": "/web/content/?model={}&id={}&field=file_data&filename={}&download=true".format(
                    self._name, self.id, "ReplenishmentReport-%s to %s.xlsx" % (start_date, end_date)
                ),
                "target": "self",
            }

    def _get_master_variants(self, products):
        """
        Prepare list of all master_product_id from version as well as master it self.
        """
        master_variants = self.env["product.product"]
        for product in products:
            master_product = product.master_product_id or product
            master_variants |= master_product.product_variant_id
        return master_variants

    def _prepare_report_data(self):
        data = []
        total_days = (self.end_date - self.start_date).days + 1
        # Selected products will be conbination of the Master and Version products
        # But stock move will be always Master product, So, get master product first
        customer_location_id = self.env.ref("stock.stock_location_customers").id
        domain = [
            ("date", ">=", self.start_date),
            ("date", "<=", self.end_date),
            "|",
            ("location_id", "=", customer_location_id),
            ("location_dest_id", "=", customer_location_id),
            ("state", "=", "done"),
            ("partner_id.exclude_replenishment_report", "!=", True),
        ]
        if self.product_ids:
            master_variants = self._get_master_variants(self.product_ids)
            domain = AND([domain, [("product_id", "in", master_variants.ids)]])
        stock_moves = self.env["stock.move"].search(domain)
        # Prepare data based on master product
        for master in stock_moves.product_id:
            master_product = master.product_tmpl_id
            mth_sales_cl = sust_mth_oh = sust_mth_oh_po = mth_sales_bk = 0
            related_move_ids = stock_moves.filtered(lambda x: x.product_id == master)
            # demand below threshold, CL otherwise BK
            cl_moves = related_move_ids.filtered(lambda x: x.product_qty < self.bulk_threshold)
            bk_moves = related_move_ids - cl_moves

            cl_receipt_moves = cl_moves.filtered(lambda x: x.location_id.id == customer_location_id)
            cl_delivery_moves = cl_moves - cl_receipt_moves
            bk_receipt_moves = bk_moves.filtered(lambda x: x.location_id.id == customer_location_id)
            bk_delivery_moves = bk_moves - bk_receipt_moves

            total_demand_cl = sum(cl_delivery_moves.mapped("product_qty")) - sum(cl_receipt_moves.mapped("product_qty"))
            total_demand_bk = sum(bk_delivery_moves.mapped("product_qty")) - sum(bk_receipt_moves.mapped("product_qty"))
            mth_sales_cl = (total_demand_cl / total_days) * 30
            mth_sales_bk = (total_demand_bk / total_days) * 30
            if mth_sales_cl > 0:
                sust_mth_oh = master_product.qty_total / mth_sales_cl
                sust_mth_oh_po = (master_product.qty_total + master_product.qty_on_po) / mth_sales_cl
            # Find version product and master product from self.product_ids and add master product data
            related_versions = self.product_ids.filtered(
                lambda x: x.master_product_id.id == master_product.id or x.id == master_product.id
            )
            for p in related_versions:
                data.append({
                    "default_code": p.default_code,
                    "master_no": master.default_code,
                    "total_cl": total_demand_cl,
                    "total_bk": total_demand_bk,
                    "mth_sales_cl": mth_sales_cl,
                    "mth_sales_bk": mth_sales_bk,
                    "sust_mth_oh": sust_mth_oh,
                    "sust_mth_oh_po": sust_mth_oh_po,
                    "free_inventory": master.qty_total,
                    "po": master.qty_on_po,
                    "quick_service": master.quick_service,
                    "vendor": master.seller_ids[:1].partner_id.name,
                    "vendor_item_no": master.vendor_item_no,
                    "attribute_details": master.attribute_details,
                    "product_name": master.name,
                    "version_family": master.version_family,
                })
        return data
