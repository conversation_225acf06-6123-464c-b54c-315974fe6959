#!/bin/bash

# Odoo 开发环境集成分支设置脚本
# 用于创建一个包含所有插件完整代码的开发分支

set -e

echo "🚀 Setting up integrated development environment..."

# 1. 创建或切换到集成开发分支
INTEGRATION_BRANCH="develop-integration-panfu"

if git show-ref --verify --quiet refs/heads/$INTEGRATION_BRANCH; then
    echo "📋 Switching to existing integration branch: $INTEGRATION_BRANCH"
    git checkout $INTEGRATION_BRANCH
else
    echo "🌟 Creating new integration branch: $INTEGRATION_BRANCH"
    git checkout -b $INTEGRATION_BRANCH develop
fi

# 2. 合并所有 panfu 相关的功能分支
echo "🔄 Merging all panfu plugin branches..."

# 获取所有远程 panfu 相关分支
PANFU_BRANCHES=$(git branch -r | grep -E "(panfu|field-creation-restriction|stock-move-line|payment-method)" | grep -v HEAD | sed 's/origin\///' | tr -d ' ')

for branch in $PANFU_BRANCHES; do
    if [[ $branch != $INTEGRATION_BRANCH ]]; then
        echo "🔀 Merging origin/$branch..."
        git merge origin/$branch --no-edit || {
            echo "⚠️  Merge conflict detected for $branch"
            echo "Please resolve conflicts manually and run: git commit"
            echo "Then continue with: git merge --continue"
            exit 1
        }
    fi
done

echo "✅ Integration branch setup complete!"
echo ""
echo "📝 Usage Instructions:"
echo "1. Use '$INTEGRATION_BRANCH' branch for daily development and testing"
echo "2. When ready to commit feature changes, cherry-pick to specific feature branch"
echo "3. Example workflow:"
echo "   - Work on $INTEGRATION_BRANCH (complete environment)"
echo "   - git cherry-pick <commit-hash> to feature/your-specific-feature"
echo "   - Push feature branch for PR"
echo ""
echo "🔧 Current branch: $(git branch --show-current)"
echo "📦 Available plugins:"
ls -1 | grep "_panfu$" | sed 's/^/   - /'
