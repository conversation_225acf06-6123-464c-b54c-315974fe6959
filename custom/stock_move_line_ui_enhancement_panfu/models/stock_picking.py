from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class StockPicking(models.Model):
    _inherit = 'stock.picking'
    
    def action_detailed_operations(self):
        """重写详细操作按钮，根据操作类型显示不同的视图"""
        self.ensure_one()
        
        _logger.info("=== action_detailed_operations called ===")
        _logger.info(f"Picking: {self.name}, Type Code: {self.picking_type_code}")
        
        # 获取基础action
        action = self.env["ir.actions.actions"]._for_xml_id("stock.stock_move_line_action")
        
        # 设置domain只显示当前picking的记录
        action['domain'] = [('picking_id', '=', self.id)]
        
        # 根据picking_type_code选择不同的tree视图
        if self.picking_type_code == 'internal':
            # 内部调拨使用专门的视图
            try:
                tree_view_id = self.env.ref('stock_move_line_ui_enhancement_panfu.view_move_line_tree_internal').id
                _logger.info(f"Using internal view, ID: {tree_view_id}")
                action['views'] = [(tree_view_id, 'tree'), (False, 'form')]
                action['view_id'] = tree_view_id
                action['view_mode'] = 'tree,form'
            except Exception as e:
                _logger.error(f"Error loading internal view: {e}")
        elif self.picking_type_code == 'incoming':
            # 收货使用专门的视图
            try:
                tree_view_id = self.env.ref('stock_move_line_ui_enhancement_panfu.view_move_line_tree_receipt').id
                _logger.info(f"Using receipt view, ID: {tree_view_id}")
                action['views'] = [(tree_view_id, 'tree'), (False, 'form')]
                action['view_id'] = tree_view_id
                action['view_mode'] = 'tree,form'
            except Exception as e:
                _logger.error(f"Error loading receipt view: {e}")
        
        # 传递context
        action['context'] = {
            'default_picking_id': self.id,
            'default_location_id': self.location_id.id,
            'default_location_dest_id': self.location_dest_id.id,
            'default_company_id': self.company_id.id,
            'picking_type_code': self.picking_type_code,
            'picking_location_id': self.location_id.id,  # 添加这个用于 quant_id 的 domain
            'search_default_todo': 1,
        }
        
        # 设置窗口名称
        if self.picking_type_code == 'internal':
            action['name'] = f'内部调拨详细操作 - {self.name}'
        elif self.picking_type_code == 'incoming':
            action['name'] = f'收货详细操作 - {self.name}'
        
        _logger.info(f"Final action: {action}")
        
        return action
