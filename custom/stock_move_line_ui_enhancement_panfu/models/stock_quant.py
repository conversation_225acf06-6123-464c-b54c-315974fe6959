from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class StockQuant(models.Model):
    _inherit = 'stock.quant'

    # 添加计算字段用于直接访问位置和批次名称
    location_name = fields.Char(
        string="Location Name",
        compute='_compute_display_names',
        store=False
    )

    lot_name = fields.Char(
        string="Lot Name",
        compute='_compute_display_names',
        store=False
    )

    @api.depends('location_id', 'lot_id')
    def _compute_display_names(self):
        """计算位置和批次名称"""
        for quant in self:
            quant.location_name = quant.location_id.name if quant.location_id else ''
            quant.lot_name = quant.lot_id.name if quant.lot_id else ''
    
    def name_get(self):
        """自定义 quant 的显示名称，格式：location_name - lot_name"""
        # 检查是否被其他模块重写
        print(f"[STOCK_QUANT] Custom name_get method called")
        result = []
        for quant in self:
            # 使用print确保输出，因为日志级别可能过滤了info级别的日志
            # print(f"[STOCK_QUANT] name_get called for quant {quant.id}")
            # print(f"[STOCK_QUANT] Location ID: {quant.location_id}")
            # print(f"[STOCK_QUANT] Location Name: {quant.location_id.name if quant.location_id else 'None'}")
            # print(f"[STOCK_QUANT] Lot ID: {quant.lot_id}")
            # print(f"[STOCK_QUANT] Lot Name: {quant.lot_id.name if quant.lot_id else 'None'}")
            # print(f"[STOCK_QUANT] Quantity: {quant.quantity}")

            # 构建显示名称：位置 - 批次信息
            display_parts = []

            # 添加位置信息
            if quant.location_id and quant.location_id.name:
                location_name = quant.location_id.name
                display_parts.append(location_name)
                print(f"[STOCK_QUANT] Added location: {location_name}")

            # 添加批次信息
            if quant.lot_id and quant.lot_id.name:
                lot_name = quant.lot_id.name
                display_parts.append(lot_name)
                print(f"[STOCK_QUANT] Added lot: {lot_name}")

            # 组合显示名称：location_name - lot_name
            name = " - ".join(filter(None, display_parts))
            print(f"[STOCK_QUANT] Final display name: {name}")
            result.append((quant.id, name))

        print(f"[STOCK_QUANT] name_get result: {result}")
        return result

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        """扩展搜索功能，支持按位置名称、批次名称等搜索"""
        print(f"[STOCK_QUANT] _name_search called with name: {name}")
        args = args or []
        
        if name:
            # 支持按位置名称搜索
            location_domain = [('location_id.name', operator, name)]
            # 支持按批次名称搜索
            lot_domain = [('lot_id.name', operator, name)]
            # 组合搜索条件
            search_domain = ['|'] + location_domain + ['|'] + lot_domain + [('id', operator, name)]
            args = search_domain + args
        
        result = super(StockQuant, self)._name_search(name, args, operator, limit, name_get_uid)
        print(f"[STOCK_QUANT] _name_search result: {result}")
        return result