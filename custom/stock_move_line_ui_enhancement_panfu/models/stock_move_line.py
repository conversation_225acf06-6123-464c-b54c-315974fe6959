from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    # 添加 quant_id 字段用于选择库存
    quant_id = fields.Many2one(
        'stock.quant',
        string="Pick From",
        help="Select the specific stock quant to pick from",
        store=True,
        index=True,
        ondelete='set null',  # 当关联的stock.quant被删除时，将此字段设为空
        domain="[('location_id.usage', '=', 'internal'), ('quantity', '>', 0.01)]"  # 只能选择有库存的内部位置，避免浮点数精度问题
    )

    # 添加计算字段用于显示完整的 quant 信息
    quant_display_name = fields.Char(
        string="Pick From Display",
        compute='_compute_quant_display_name',
        store=False
    )

    # 重写或创建 display_warning 字段
    display_warning = fields.Bo<PERSON>an(
        string="Display Warning",
        compute='_compute_display_warning',
        store=False,
        readonly=True,  # 设置为只读
        help="Transfer quantity is different from the LOT available quantity. Please DOUBLE check."
    )
    
    # 动态计算help信息的字段（可选，如果需要动态提示）
    display_warning_help = fields.Char(
        string="Warning Help",
        compute='_compute_display_warning',
        store=False
    )
    
    @api.depends('quant_id', 'quant_id.location_id', 'quant_id.lot_id')
    def _compute_quant_display_name(self):
        """计算 quant 的完整显示名称"""
        for line in self:
            if line.quant_id:
                quant = line.quant_id
                # 构建显示名称：位置 - 批次信息
                display_parts = []

                if quant.location_id:
                    display_parts.append(quant.location_id.name)

                if quant.lot_id and quant.lot_id.name:
                    display_parts.append(quant.lot_id.name)

                line.quant_display_name = " - ".join(filter(None, display_parts))
            else:
                line.quant_display_name = ""

    @api.onchange('quant_id')
    def _onchange_quant_id(self):
        """当选择 quant 时，自动填充相关字段"""
        print(f"[STOCK_MOVE_LINE] _onchange_quant_id called with quant_id: {self.quant_id}")
        if self.quant_id:
            print(f"[STOCK_MOVE_LINE] Quant details - Location: {self.quant_id.location_id}, Lot: {self.quant_id.lot_id}")
            # 自动填充位置信息
            if self.quant_id.location_id:
                self.location_id = self.quant_id.location_id

            # 自动填充批次信息
            if self.quant_id.lot_id:
                self.lot_id = self.quant_id.lot_id
                self.lot_name = self.quant_id.lot_id.name

            # 自动填充产品信息
            if self.quant_id.product_id:
                self.product_id = self.quant_id.product_id

    @api.depends('quantity', 'lot_qty_available', 'lot_id')
    def _compute_display_warning(self):
        """计算是否显示警告"""
        for line in self:
            if line.lot_id and line.quantity and line.lot_qty_available:
                # 允许一定的浮点数误差
                if abs(line.quantity - line.lot_qty_available) > 0.01:
                    line.display_warning = True
                    line.display_warning_help = "Transfer quantity is different from the LOT available quantity. Please DOUBLE check."
                else:
                    line.display_warning = False
                    line.display_warning_help = ""
            else:
                line.display_warning = False
                line.display_warning_help = ""