from odoo import fields, models


class StockLocation(models.Model):
    _inherit = "stock.location"

    is_cutting_location = fields.Boolean("Cutting Location?")

    def _get_cutting_location(self):
        """
        Returns the cutting location for the current company.
        """
        company_id = self.env.company.id
        return self.search(
            [
                *self._check_company_domain(company_id),
                ("is_cutting_location", "=", True)
            ],
            limit=1)
