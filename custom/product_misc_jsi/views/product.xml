<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="product_template_only_form_view_product_misc_jsi" model="ir.ui.view">
            <field name="name">product.template.product.form.product.misc.jsi</field>
            <field name="model">product.template</field>
            <field name="priority">90</field>
            <field name="inherit_id" ref="product.product_template_only_form_view" />
            <field name="arch" type="xml">
                <header position="inside">
                    <button name="button_update_version_products" type="object" string="Update version products" invisible="not is_master" />
                    <button name="button_update_from_master_products" type="object" string="Update from Master" invisible="not master_product_id" />
                </header>
                <button name="open_pricelist_rules" position="before">
                    <button name="action_view_on_cutting_moves" type="object" icon="fa-exchange" class="oe_stat_button">
                        <div class="o_field_widget o_stat_info">
                             <span class="o_stat_value">
                                 <field name="qty_on_cutting" digits="[12,2]" />
                                 <field name="uom_name"/>
                             </span>
                             <span class="o_stat_text">Cutting</span>
                        </div>
                    </button>
                    <button name="action_view_on_stock_quants" type="object" icon="fa-exchange" class="oe_stat_button">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value">
                                <field name="qty_on_stock" digits="[12,2]" class="oe_inline" />
                                <field name="uom_name" class="oe_inline"/>
                            </span>
                            <span class="o_stat_text">Stock</span>
                        </div>
                    </button>
                    <button name="action_view_free_inv_moves" type="object" icon="fa-exchange" class="oe_stat_button">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value">
                                <field name="qty_total" digits="[12,2]" class="oe_inline" />
                                <field name="uom_name" class="oe_inline"/>
                            </span>
                            <span class="o_stat_text">Free Inventory</span>
                        </div>
                    </button>
                    <button name="action_view_pending_po_moves" type="object" icon="fa-exchange" class="oe_stat_button">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value">
                                <field name="qty_on_po" digits="[12,2]" class="oe_inline" />
                                <field name="uom_name" class="oe_inline"/>
                            </span>
                            <span class="o_stat_text">PO QTY</span>
                        </div>
                    </button>
                    <button name="action_view_pending_so_moves" type="object" icon="fa-exchange" class="oe_stat_button">
                        <div class="o_field_widget o_stat_info">
                            <span class="o_stat_value">
                                <field name="qty_on_so" nolabel="1" digits="[12,2]" class="oe_inline" />
                                <field name="uom_name" nolabel="1" class="oe_inline"/>
                            </span>
                            <span class="o_stat_text">Pending SO</span>
                        </div>
                    </button>
                    <button class="oe_stat_button" name="action_view_version_products" string="Version Products" type="object" icon="fa-bars" invisible="not version_product_ids" />
                    <button name="action_view_stock_move_lines" position="move" />
                    <!--<button name="action_open_quants" position="move" />
                    <button name="action_product_tmpl_forecast_report" position="move" />
                    <button name="action_open_product_lot" position="move" />
                    <button name="action_view_related_putaway_rules" position="move" /> -->
                </button>
                <xpath expr="//field[@name='description']/.." position="replace" />
                <xpath expr="//field[@name='default_code']" position="replace" />
                <div class="oe_title" position="replace">
                    <div class="oe_title">
                        <field name="is_development" invisible="True" />
                        <h1>
                            <field name="default_code" nolabel="1" placeholder="Item No." />
                        </h1>
                        <group>
                            <field name="name" placeholder="Product Name" string="Product Name" force_save="True"/>
                            <field name="master_product_id" domain="[('is_master', '=', True)]" />
                            <field name="rank" />
                            <field name="rank_number" />
                        </group>
                    </div>
                </div>
                <xpath expr="//page[@name='general_information']/group" position="before">
                    <group>
                        <group name="group_right1">
                            <field name="description" />
                            <field name="brand_id" />
                            <field name="sample_bookcat" />
                            <field name="collection_name_full" />
                            <field name="equiv_super_lv" />
                            <field name="average_weight_g" />
                            <field name="width_cm" />
                            <field name="product_feature"/>
                            <field name="region_of_origin_id" />
                            <field name="shipping_info" />
                            <field name="ecom_remarks" />
                            <field name="vendor_item_no" />
                            <field name="attribute_details" />
                        </group>
                        <group name="group_left1">
                            <field name="stock_availability" />
                            <field name="temp_out_until" readonly="master_product_id and stock_availability in [False, 'oos']" required="stock_availability == 'temp_out'" />
                            <field name="quick_service" />
                            <field name="keep_stock" />
                            <field name="same_product_ids" widget="many2many_tags" />
                            <field name="high_similar_ids" widget="many2many_tags" placeholder="相似到可以配褲" />
                            <field name="med_similar_ids" widget="many2many_tags" help="相似，但不足以配褲" placeholder="相似，但不足以配褲" string="Mid Similar" />
                            <field name="low_similar_ids" widget="many2many_tags" placeholder="僅僅是近似" />
                            <field name="secondary_uom_list_price" widget="monetary"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="lab_origin"/>
                            <field name="lab_content"/>
                            <field name="lab_super"/>
                            <field name="lab_weight" />
                        </group>
                        <group>
                            <field name="lab_width"/>
                            <field name="lab_feature"/>
                            <field name="lab_colour"/>
                        </group>
                    </group>
                    <br/>
                    <group>
                        <group>
                            <field name="product_colour_ids" widget="many2many_tags" string="Colour" />
                            <field name="product_pattern_ids" widget="many2many_tags" string="Pattern" />
                            <field name="product_compo_ids" widget="many2many_tags" string="Composition" />
                            <field name="collection_id" />
                            <field name="product_yarn_id" />
                            <field name="secondary_uom_id" />
                            <field name="solids" />
                            <field name="double_face" />
                        </group>
                        <group>
                            <field name="categ_id" position="move" />
                            <field name="detailed_type" position="move" />
                            <field name="sub_categ_id" required="True" />
                            <field name="public_categ_ids" widget="many2many_tags" position="move" />
                            <field name="is_master" readonly="True" />
                            <field name="master_collection_id" />
                            <field name="nav_no" />
                            <field name="related_sample_book_ids" widget="many2many_tags" placeholder="SBxxxx, Sample books that incorporate this product" />
                            <field name="create_date" />
                            <field name="write_date" />
                            <field name="write_uid" />
                            <field name="version_family" readonly="True" />
                        </group>
                    </group>
                    <separator string="Version Products" />
                    <field name="version_product_ids">
                        <tree create="false" edit="false" delete="false">
                            <field name="default_code" />
                            <field name="secondary_uom_list_price" />
                            <field name="brand_id" />
                            <field name="collection_id" />
                            <field name="sample_bookcat" />
                            <field name="create_date" />
                        </tree>
                    </field>
                </xpath>
                <field name="temp_out_until" position="after">
                    <label for="allow_out_of_stock_order" position="move" />
                    <xpath expr="//field[@name='allow_out_of_stock_order']/.." position="move" />
                </field>
                <field name="double_face" position="after">
                    <label for="standard_price" position="move"/>
                    <div name="standard_price_uom" position="move"/>
                </field>
                <field name="secondary_uom_list_price" position="after">
                    <label for="list_price"/>
                    <div name="pricing" class="o_row">
                        <field name="list_price" class="oe_inline" widget="monetary" options="{'currency_field': 'currency_id', 'field_digits': True}"/>
                    </div>
                </field>
                <field name="secondary_uom_id" position="after">
                    <field name="uom_id" position="move" />
                    <field name="uom_po_id" position="move" />
                    <!-- <xpath expr="//group[@name='group_general']/field[@name='uom_po_id']" position="move" /> --> -->
                </field>
                <group name="properties" position="inside">
                    <group>
                        <field name="invoice_policy" />
                        <field name="expense_policy" />
                    </group>
                </group>
                <xpath expr="//field[@name='website_url']" position="before">
                    <field name="seo_name"/>
                    <field name="is_seo_optimized"/>
                </xpath>
                <xpath expr="//field[@name='website_url']" position="attributes">
                    <attribute name="invisible" />
                </xpath>
                <field name="public_categ_ids" position="attributes">
                    <attribute name="string">Ecom Categories</attribute>
                </field>
                <!-- Need to hide second fields after moving its position -->
                <!-- <xpath expr="//group[@name='group_general']/field[@name='uom_po_id']" position="replace" /> -->
                <xpath expr="//group[@name='group_general']/field[@name='detailed_type']" position="replace" />
                <group name="group_standard_price" position="attributes">
                    <attribute name="invisible">1</attribute>
                </group>
                <xpath expr="//field[@name='responsible_id']" position="after">
                    <field name="low_weight_g" force_save="1" />
                    <field name="high_weight_g" force_save="1" />
                    <xpath expr="//label[@for='weight']" position="move"/>
                    <xpath expr="//div[@name='weight']" position="move"/>
                    <field name="weight_range_g"/>
                    <field name="weight_gm2"/>
                </xpath>
                <label for="route_ids" position="before">
                    <xpath expr="//field[@name='responsible_id']" position="move"/>
                    <xpath expr="//group[@name='traceability']/field[@name='tracking']" position="move"/>
                </label>
                <xpath expr="//group[@name='traceability']" position="replace" />
            </field>
        </record>

        <record id="product_template_form_view_bom_button_product_misc_jsi" model="ir.ui.view">
            <field name="name">product.template.form.view.bom.button.product.misc.jsi</field>
            <field name="model">product.template</field>
            <field name="priority">90</field>
            <field name="inherit_id" ref="mrp.product_template_form_view_bom_button" />
            <!-- <field name="groups_id" eval="[(4, ref('mrp.group_mrp_user'))]" /> -->
            <field name="arch" type="xml">
                <button name="action_product_tmpl_forecast_report" position="after">
                    <button name="action_used_in_bom" position="move" />
                    <xpath expr="//field[@name='bom_count']/.." position="move" />
                </button>
            </field>
        </record>

        <record id="view_stock_product_template_tree_product_misc_jsi" model="ir.ui.view">
            <field name="name">view.stock.product.template.tree.view.product.misc.jsi</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="stock.view_stock_product_template_tree" />
            <field name="priority">99</field>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='responsible_id']" position="replace">
                    <field name="is_master" />
                    <field name="rank" widget="boolean_toggle" readonly="1"/>
                    <field name="rank_number" />
                    <field name="attribute_details" />
                    <field name="brand_id" />
                    <field name="collection_name_full" />
                    <field name="equiv_super_lv" />
                    <field name="sample_bookcat" />
                    <field name="average_weight_g" />
                    <field name="qty_on_cutting" digits="[12,2]" />
                    <field name="qty_on_stock" digits="[12,2]" />
                    <field name="qty_on_po" digits="[12,2]" />
                    <field name="qty_total" digits="[12,2]" />
                </xpath>
                <xpath expr="//field[@name='list_price']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='standard_price']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='qty_available']" position="attributes">
                    <attribute name="optional">hide</attribute>
                    <attribute name="digits">[12,2]</attribute>
                </xpath>
                <xpath expr="//field[@name='virtual_available']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
                <xpath expr="//field[@name='uom_id']" position="attributes">
                    <attribute name="optional">hide</attribute>
                </xpath>
            </field>
        </record>

        <record id="product_template_search_view_product_misc_jsi" model="ir.ui.view">
            <field name="name">product.template.search.view.product.misc.jsi</field>
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_search_view" />
            <field name="arch" type="xml">
                <field name="name" position="before">
                    <field name="version_family" filter_domain="[('version_family', '=', self)]" />
                </field>
            </field>
        </record>

        <record id="action_update_version_product_misc_jsi" model="ir.actions.server">
            <field name="name">Update version products</field>
            <field name="model_id" ref="product.model_product_template" />
            <field name="binding_model_id" ref="product.model_product_template" />
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">records.button_update_version_products()</field>
        </record>

        <record id="action_update_from_master_product_misc_jsi" model="ir.actions.server">
            <field name="name">Update from master</field>
            <field name="model_id" ref="product.model_product_template" />
            <field name="binding_model_id" ref="product.model_product_template" />
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">records.button_update_from_master_products()</field>
        </record>
    </data>
</odoo>
