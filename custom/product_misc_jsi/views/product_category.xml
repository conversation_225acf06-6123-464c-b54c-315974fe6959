<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="product_category_form_view_product_misc_jsi" model="ir.ui.view">
            <field name="name">product.category.form.view.product.misc.jsi</field>
            <field name="model">product.category</field>
            <field name="inherit_id" ref="product.product_category_form_view" />
            <field name="arch" type="xml">
                <div class="oe_title" position="before">
                    <field name="photo1" widget="image" class="oe_avatar" />
                    <field name="photo2" widget="image" class="oe_avatar" />
                </div>
                <field name="parent_id" position="after">
                    <field name="is_development" />
                </field>
                <group name="account_property" position="after">
                    <notebook>
                        <page string="Other Information">
                            <group>
                                <group>
                                    <field name="date_start" />
                                    <field name="date_end" />
                                    <field name="brand_id" />
                                    <field name="collection_id" />
                                    <field name="other_spec" />
                                    <field name="region_of_origin" />
                                    <field name="description" />
                                </group>
                                <group>
                                    <field name="pattern_colour" />
                                    <field name="super_level" />
                                    <field name="composition" />
                                    <field name="season" />
                                    <field name="price_range" />
                                    <field name="weight_range" />
                                </group>
                            </group>
                        </page>
                    </notebook>
                </group>
            </field>
        </record>
    </data>
</odoo>
