from odoo import models


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    def write(self, vals):
        """
        OVERRIDE
        - If Quantity decreased, demand should be decreased in its related delivery order.
        - No risk there may be 2 Move lines for a single product, in this case, the user will do it manually.
        - If it's a kit, just forgot about this, do it manually.
        - No need to do this if it's from website
        """
        if self.env.context.get('website_id') or 'product_uom_qty' not in vals:
            return super().write(vals)

        old_qty = self.product_uom_qty
        res = super().write(vals)
        if old_qty > self.product_uom_qty:
            moves = self.move_ids.filtered(
                lambda m: m.product_id == self.product_id and m.state not in ["done", "cancel"]
            )
            if len(moves) == 1:
                moves.write({"product_uom_qty": self.product_uom_qty})
        return res
