<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_picking_form_delivery_automation_jsi" model="ir.ui.view">
            <field name="name">stock.picking.form.delivery.automation.jsi</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form" />
            <field name="arch" type="xml">
                <xpath
                    expr="//field[@name='move_ids_without_package']/tree/field[@name='product_uom']"
                    position="after"
                >
                    <field name="price_unit" />
                </xpath>
                <field name="move_ids_without_package" position="after">
                    <group class="oe_subtotal_footer oe_right" invisible="picking_type_code != 'incoming'">
                        <field name="po_currency_id" invisible="1" />
                        <field name="amount_total" widget="monetary" options="{'currency_field': 'po_currency_id'}"/>
                    </group>
                </field>
            </field>
        </record>
    </data>
</odoo>
