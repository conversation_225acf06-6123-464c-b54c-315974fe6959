from odoo import models, api

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    @api.onchange('partner_shipping_id')
    def _onchange_partner_shipping_id_lb_rmks_flag(self):
        if self.partner_shipping_id:
            flag = self.partner_shipping_id.lb_rmks_flag
            prefix = ''
            if flag == 'zheng':
                prefix = '正；'
            elif flag == 'fan':
                prefix = '反；'
            elif flag == '':
                prefix = 'null；'
            if prefix:
                for line in self.order_line:
                    if line.remarks:
                        if not line.remarks.startswith(('正；', '反；', 'null；')):
                            line.remarks = f'{prefix}{line.remarks}'
                    else:
                        line.remarks = prefix
        # 若未设置标记，不做处理 