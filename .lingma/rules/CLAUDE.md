---
trigger: always_on
---

# Odoo 17 项目开发规范

## 插件与目录结构

- 自定义插件放置在 `./custom` 文件夹中。
- 插件命名始终遵循：功能_作者名_任务编号，例如：`stock_move_line_ui_enhancement_panfu`。
- 本项目作者始终为：panfu。
- 每次在 models 目录下添加新的 model 文件后，都要在 `models/__init__.py` 中添加对应的导入语句，否则 Odoo 不会自动加载该模型扩展。
- 每个 spec 文件应放在对应插件目录下的 `specs/` 子目录中。

## 代码管理规范（Git Flow）

- 所有项目代码必须通过 Git 进行版本控制，采用 Git Flow 分支模型作为核心策略。
- 代码托管平台：Github
- 主要分支：
  - `main`：生产分支。永远处于可部署状态，只接受来自 release 分支和 hotfix 分支的合并，任何人都不能直接向 main 分支提交代码。
  - `develop`：主开发分支。所有新功能开发的起点和汇集点，包含所有已完成并测试的功能，准备下一个版本的发布。
- 辅助分支：
  - `feature/<feature-name>`：功能开发分支。
    - 命名规则：如 `feature/login-with-phone`, `feature/sale-order-approval`
    - 创建来源：必须从 develop 分支创建。
    - 合并目标：完成开发和自测后，发起 Pull Request (PR) 到 develop 分支。
  - `release/<version-number>`：预发布分支。
    - 命名规则：如 `release/v1.2.0`
    - 创建来源：当 develop 分支的功能足够发布一个版本时，从 develop 创建。
    - 职责：用于版本发布的最后准备工作，如最后的测试、Bug 修复、文档更新等。此分支上的 Bug 修复需要同时合并回 develop 分支。
    - 合并目标：测试完成后，合并到 main 分支（并打上 Tag），并且也必须合并回 develop 分支。
  - `hotfix/<issue-name>`：线上紧急修复分支。
    - 命名规则：如 `hotfix/login-bug-fix`
    - 创建来源：必须从 main 分支创建。
    - 职责：用于快速修复生产环境的紧急 Bug。
    - 合并目标：修复完成后，必须同时合并回 main 分支（并提升版本 Tag）和 develop 分支。
- Commit 消息规范：
  - 每次提交必须包含清晰的说明，格式为：类型(范围): 简短描述。
  - 类型 (Type)：
    - feat: 新功能 (feature)
    - fix: 修复 bug
    - docs: 文档变更
    - style: 代码格式（不影响代码运行的变动）
    - refactor: 重构（既不是增加功能，也不是修复 bug）
    - test: 增加测试
    - chore: 构建过程或辅助工具的变动
  - 示例：
    - feat(sale): 增加销售订单的自定义审批流
    - fix(stock): 修复库存计算在负库存下的错误

## 需求与验收流程

- 新需求需先梳理问题和需求，采用 EARS 简易需求语法，编写需求文档，保存在 `specs/插件名/requirements.md`。
- 需求文档需包含介绍、用户故事、验收标准（ERAS 句式）。
- 技术方案设计需保存在 `specs/插件名/design.md`，内容包括架构、技术选型、数据库/接口设计、测试策略、安全性等，必要时用 mermaid 绘图。
- 任务拆分需保存在 `specs/插件名/tasks.md`，细化为可执行的 checklist。
- 需求、设计、任务需与负责人确认后，方可进入开发阶段。

## Odoo Shell 与数据库操作

- 查询模型、字段、视图等建议优先用 Odoo shell 或 SQL，示例：
  - Odoo shell:
    ```shell
    python3 ../odoo_base/odoo/odoo-bin shell -c odoo.conf -d $(grep db_name odoo.conf | cut -d= -f2 | xargs) --no-http << 'EOF'
    # 你的 Odoo shell 代码
    EOF
    ```
  - SQL:
    ```shell
    psql -d $(grep db_name odoo.conf | cut -d= -f2) \
         -U $(grep db_user odoo.conf | cut -d= -f2) \
         -h $(grep db_host odoo.conf | cut -d= -f2) \
         -p $(grep db_port odoo.conf | cut -d= -f2) \
         -c "SELECT COUNT(*) FROM res_users WHERE active = true;"
    ```
- 当前 Odoo 版本为 17，遇到模型或模块不存在时，优先用 Context7 查官方/社区文档和 Odoo 17 源码，不要盲目假设模型名或直接本地搜索。

## Asana 任务管理

- 任务查询、跟进、更新操作需用 Asana MCP 工具，流程如下：
  1. 获取工作空间 ID
  2. 搜索任务（可按关键词、负责人、状态等筛选）
  3. 获取任务详情（推荐字段：name, completed, assignee, due_on, notes, projects, sections, custom_fields, created_at, modified_at）
  4. 查看评论获取任务全貌

## 其他规范

- 启动 Odoo 服务命令：`pkill odoo; ../odoo_base/odoo/odoo-bin -c odoo.conf --dev=all`，不指定 -d，数据库配置在 odoo.conf。
- 遇到 Odoo 技术问题（如查不到表、找不到 view 节点等），优先用 MCP Context7 查询 Odoo 17 文档。
- 用户偏好：优先提供直接 SQL 语句而非解释性说明。
- 不自动重启项目，需用户手动重启。

- 自定义插件放置在 ./custom 文件夹中；
- 本项目作者始终为：panfu
- 插件命名始终遵循： 功能_作者名，例如：stock_move_line_ui_enhancement_panfu
- 主动使用 sql 或 odoo shell 来论证你的推断，要主动执行，不要教用户做
- 回复内容尽量用中文